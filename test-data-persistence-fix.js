/**
 * AI简历项目数据持久化修复验证测试
 * 测试简历分析后积木数据的云数据库同步功能
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

const db = cloud.database();

/**
 * 测试用户OPENID
 */
const TEST_USER_OPENID = 'o16hGvi6bsSwrpnbtFIg06wVutX4';

/**
 * 模拟简历分析生成的积木数据
 */
const mockResumeAnalysisResult = {
  bricks: [
    {
      id: `test_resume_brick_${Date.now()}_1`,
      title: '测试简历分析积木1',
      category: 'skill',
      content: '这是通过简历分析生成的测试积木，用于验证数据持久化功能',
      source: 'resume_upload',
      confidence: 0.9,
      keywords: ['测试', '简历分析', '数据持久化'],
      level: '高级',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: `test_resume_brick_${Date.now()}_2`,
      title: '测试简历分析积木2',
      category: 'project',
      content: '这是第二个测试积木，验证批量保存功能',
      source: 'resume_upload',
      confidence: 0.85,
      keywords: ['项目经验', '批量保存', '云数据库'],
      level: '中级',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
  ]
};

/**
 * 测试1: 模拟简历分析后的积木数据保存
 */
async function testResumeAnalysisBricksSave() {
  console.log('\n🧪 测试1: 模拟简历分析后的积木数据保存');
  
  try {
    const bricks = mockResumeAnalysisResult.bricks;
    console.log(`📝 准备保存 ${bricks.length} 个积木到云数据库`);
    
    // 模拟resumeWorker中的saveBricksToDatabase函数
    const savePromises = bricks.map(async (brick, index) => {
      const brickData = {
        ...brick,
        _openid: TEST_USER_OPENID,
        isActive: true,
        usageCount: 0
      };
      
      const result = await db.collection('bricks').add({
        data: brickData
      });
      
      console.log(`✅ 积木${index + 1}保存成功: ${brick.title}`);
      return result;
    });
    
    await Promise.all(savePromises);
    console.log('✅ 所有积木数据保存到云数据库成功');
    
    return true;
  } catch (error) {
    console.error('❌ 积木数据保存失败:', error);
    return false;
  }
}

/**
 * 测试2: 验证云函数brickManager能否正确查询数据
 */
async function testBrickManagerQuery() {
  console.log('\n🧪 测试2: 验证云函数brickManager查询功能');
  
  try {
    // 模拟前端调用brickManager云函数
    const result = await cloud.callFunction({
      name: 'brickManager',
      data: {
        action: 'list',
        testMode: true,
        testOpenid: TEST_USER_OPENID,
        data: {
          limit: 100,
          sortBy: 'updateTime',
          sortOrder: 'desc'
        }
      }
    });
    
    console.log('🔍 云函数调用结果:', {
      errMsg: result.errMsg,
      statusCode: result.result?.statusCode
    });
    
    if (result.result?.body) {
      const responseData = JSON.parse(result.result.body);
      const bricks = responseData.data?.bricks || [];
      console.log(`✅ 云函数返回 ${bricks.length} 个积木`);
      
      // 检查是否包含刚才保存的测试积木
      const testBricks = bricks.filter(brick => 
        brick.title && brick.title.includes('测试简历分析积木')
      );
      console.log(`🎯 找到 ${testBricks.length} 个测试积木`);
      
      return bricks.length > 0;
    } else {
      console.error('❌ 云函数返回数据格式异常');
      return false;
    }
  } catch (error) {
    console.error('❌ 云函数查询失败:', error);
    return false;
  }
}

/**
 * 测试3: 验证数据持久化（模拟页面刷新）
 */
async function testDataPersistence() {
  console.log('\n🧪 测试3: 验证数据持久化（模拟页面刷新）');
  
  try {
    // 直接查询数据库，模拟页面刷新后重新加载
    const result = await db.collection('bricks')
      .where({
        _openid: TEST_USER_OPENID
      })
      .orderBy('updateTime', 'desc')
      .limit(10)
      .get();
    
    console.log(`📊 数据库中共有 ${result.data.length} 个积木`);
    
    // 检查最近的积木是否包含测试数据
    const recentTestBricks = result.data.filter(brick => 
      brick.title && brick.title.includes('测试简历分析积木')
    );
    
    console.log(`🔍 最近的测试积木数量: ${recentTestBricks.length}`);
    
    if (recentTestBricks.length > 0) {
      console.log('✅ 数据持久化验证成功 - 页面刷新后数据依然存在');
      return true;
    } else {
      console.warn('⚠️ 未找到测试积木数据');
      return false;
    }
  } catch (error) {
    console.error('❌ 数据持久化验证失败:', error);
    return false;
  }
}

/**
 * 测试4: 清理测试数据
 */
async function cleanupTestData() {
  console.log('\n🧹 清理测试数据');
  
  try {
    const result = await db.collection('bricks')
      .where({
        _openid: TEST_USER_OPENID,
        title: db.RegExp({
          regexp: '测试简历分析积木',
          options: 'i'
        })
      })
      .remove();
    
    console.log(`🗑️ 清理了 ${result.stats.removed} 条测试数据`);
    return true;
  } catch (error) {
    console.error('❌ 清理测试数据失败:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runDataPersistenceTest() {
  console.log('🚀 开始AI简历项目数据持久化修复验证测试');
  console.log(`👤 测试用户OPENID: ${TEST_USER_OPENID}`);
  
  const results = {
    save: false,
    query: false,
    persistence: false,
    cleanup: false
  };
  
  try {
    // 执行测试
    results.save = await testResumeAnalysisBricksSave();
    results.query = await testBrickManagerQuery();
    results.persistence = await testDataPersistence();
    results.cleanup = await cleanupTestData();
    
    // 生成测试报告
    console.log('\n📋 测试结果汇总:');
    console.log(`- 积木数据保存: ${results.save ? '✅ 通过' : '❌ 失败'}`);
    console.log(`- 云函数查询: ${results.query ? '✅ 通过' : '❌ 失败'}`);
    console.log(`- 数据持久化: ${results.persistence ? '✅ 通过' : '❌ 失败'}`);
    console.log(`- 测试数据清理: ${results.cleanup ? '✅ 通过' : '❌ 失败'}`);
    
    const allPassed = results.save && results.query && results.persistence;
    
    if (allPassed) {
      console.log('\n🎉 所有核心测试通过！数据持久化修复成功！');
      console.log('✅ 简历分析后的积木数据能够正确保存到云数据库');
      console.log('✅ 页面刷新后数据不会丢失');
    } else {
      console.log('\n⚠️ 部分测试失败，需要进一步检查');
    }
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return false;
  }
}

// 导出测试函数
module.exports = {
  runDataPersistenceTest,
  testResumeAnalysisBricksSave,
  testBrickManagerQuery,
  testDataPersistence,
  cleanupTestData
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runDataPersistenceTest().then(success => {
    process.exit(success ? 0 : 1);
  });
}
