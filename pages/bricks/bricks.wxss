/* pages/bricks/bricks.wxss */
/* 积木库页面 - 蓝白渐变元气风格 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
  padding-bottom: 120rpx;
  position: relative;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 32rpx 24rpx;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1565c0;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(21, 101, 192, 0.2);
}

.header-subtitle {
  font-size: 28rpx;
  color: #1976d2;
  opacity: 0.8;
}



/* 批量操作栏 */
.batch-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 998;
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.2);
  border-bottom: 2rpx solid #e3f2fd;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.batch-bar.active {
  transform: translateY(0);
}

.batch-text {
  font-size: 28rpx;
  color: #1565c0;
  font-weight: 600;
}

.batch-actions {
  display: flex;
  gap: 12rpx;
}

.batch-btn {
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.cancel-btn:active {
  background: linear-gradient(135deg, #eeeeee 0%, #e0e0e0 100%);
  transform: scale(0.95);
}

.delete-btn {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(244, 67, 54, 0.3);
}

.delete-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.4);
}

/* 上传区域 */
.upload-section {
  margin: 0 32rpx 24rpx;
}

.upload-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.upload-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.upload-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  z-index: 1;
}

.upload-text {
  flex: 1;
  z-index: 1;
}

.upload-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.upload-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  line-height: 1.4;
}

.upload-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  backdrop-filter: blur(10rpx);
  z-index: 1;
  transition: all 0.3s ease;
}

.upload-btn:disabled {
  opacity: 0.6;
}

.upload-btn::after {
  border: none;
}

.upload-btn:not(:disabled):active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}



/* 筛选区域 */
.filter-section {
  margin: 0 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(33, 150, 243, 0.15);
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  gap: 12rpx;
}

.filter-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
  border: 2rpx solid #e3f2fd;
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.filter-tab.active {
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
  border-color: #1976d2;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
  transform: translateY(-2rpx);
}

.tab-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 4rpx;
  transition: all 0.3s ease;
}

.filter-tab.active .tab-text {
  color: white;
  font-weight: 600;
}

.tab-count {
  font-size: 22rpx;
  color: #999;
  font-weight: 400;
  transition: all 0.3s ease;
}

.filter-tab.active .tab-count {
  color: rgba(255, 255, 255, 0.9);
}

/* 积木区域 */
.bricks-section {
  margin: 0 32rpx;
  padding-bottom: 120rpx;
}

/* 模块区域 */
.module-section {
  margin-bottom: 48rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 区域头部 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 8rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #1565c0;
}

.section-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.2);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
}

.action-btn .icon {
  font-size: 20rpx;
  color: #1565c0;
  font-weight: 600;
}

/* 积木网格 - 修改为垂直布局 */
.bricks-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.brick-item {
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  border-radius: 20rpx;
  padding: 24rpx;
  border: 2rpx solid #e3f2fd;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.brick-item.selected {
  border-color: #42a5f5;
  background: linear-gradient(135deg, #f3f9ff 0%, #e8f4fd 100%);
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
  transform: translateY(-2rpx);
}

.brick-item:active {
  transform: scale(0.98);
}

/* 选择指示器 */
.select-indicator {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  z-index: 10;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  border-color: #42a5f5;
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
}

.check-icon {
  font-size: 18rpx;
  color: white;
  font-weight: 600;
}



/* 积木内容 */
.brick-content {
  padding-top: 8rpx;
}

.brick-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  flex-shrink: 0;
}

.brick-icon {
  font-size: 44rpx;
  filter: drop-shadow(0 2rpx 8rpx rgba(33, 150, 243, 0.3));
}

.brick-tag {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 500;
  border: 1rpx solid #90caf9;
}

.brick-body {
  flex: 1;
  margin-bottom: 16rpx;
}

.brick-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1565c0;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.brick-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 16rpx;
}



/* 关联技能和经历 */
.related-skills,
.related-experience {
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.related-title {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.skill-tags,
.experience-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.skill-tag,
.experience-tag {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid #90caf9;
}

.experience-tag {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #7b1fa2;
  border: 1rpx solid #ce93d8;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  border-radius: 24rpx;
  margin: 0 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.15);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  filter: drop-shadow(0 4rpx 16rpx rgba(33, 150, 243, 0.2));
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1565c0;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 28rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
  color: white;
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.4);
}

.btn-icon {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10rpx); 微信小程序不支持，已注释 */
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e3f2fd;
  border-top: 6rpx solid #42a5f5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #1565c0;
  font-weight: 500;
}

/* 积木详情弹窗 */
.brick-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: rgba(0, 0, 0, 0.5);
  /* backdrop-filter: blur(8rpx); 微信小程序不支持，已注释 */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.brick-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 90vw;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  transform: scale(0.8) translateY(60rpx);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 2rpx solid rgba(66, 165, 245, 0.2);
}

.brick-detail-modal.show .modal-content {
  transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
  padding: 32rpx 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #e3f2fd;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1565c0;
  text-shadow: 0 2rpx 8rpx rgba(21, 101, 192, 0.2);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(244, 67, 54, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #f44336;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modal-close:active {
  background: rgba(244, 67, 54, 0.2);
  transform: scale(0.9);
}

/* 模态框主体 */
.modal-body {
  padding: 0 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 详情区块 */
.detail-section {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  /* backdrop-filter: blur(8rpx); 微信小程序不支持，已注释 */
  border-radius: 20rpx;
  border: 2rpx solid rgba(66, 165, 245, 0.1);
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.detail-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
}

.detail-info {
  flex: 1;
}

.detail-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1565c0;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.detail-category {
  font-size: 24rpx;
  color: #666;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1565c0;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.section-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  text-align: justify;
}

/* 关键词 */
.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.keyword {
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
  color: white;
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(66, 165, 245, 0.3);
}



/* 模态框底部 */
.modal-footer {
  padding: 24rpx 32rpx 32rpx;
  display: flex;
  gap: 16rpx;
  border-top: 2rpx solid #e3f2fd;
  margin-top: 32rpx;
}

.btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.btn-secondary:active {
  background: linear-gradient(135deg, #eeeeee 0%, #e0e0e0 100%);
  transform: scale(0.98);
}

.btn-primary {
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(66, 165, 245, 0.4);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
  filter: drop-shadow(0 4rpx 16rpx rgba(0, 0, 0, 0.1));
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  /* backdrop-filter: blur(8rpx); 微信小程序不支持，已注释 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  padding: 40rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e3f2fd;
  border-top: 6rpx solid #42a5f5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 能力积木特殊样式 */
.capability-brick {
  border-left: 6rpx solid #ff6b35;
  background: linear-gradient(135deg, #fff8f5 0%, #fff 100%);
}

.capability-brick .brick-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.capability-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.capability-tags .tag {
  background: #ff6b35;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.brick-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  font-style: italic;
}

.related-work {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.related-work .company {
  color: #ff6b35;
  font-weight: 500;
}

.related-work .position {
  color: #666;
}

.related-work .period {
  color: #999;
}