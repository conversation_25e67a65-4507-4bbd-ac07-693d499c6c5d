// app.js
App({
  globalData: {
    userInfo: null,
    bricks: [], // 用户的能力积木
    templates: [], // 简历模板
    currentResume: null, // 当前编辑的简历
    theme: 'dark', // 主题设置
    isLoggedIn: false, // 登录状态
    resumes: [],
    useMockData: false, // 默认使用云开发模式
    isCloudConnected: false, // 云开发连接状态
    cloudEnvId: 'zemuresume-4gjvx1wea78e3d1e' // 云开发环境ID
  },

  onLaunch() {
    console.log('zemu积木简历小程序启动')

    // 首先加载基础服务
    this.initializeServices()

    // 修复登录时间数据（在应用启动时立即执行）
    this.fixLoginTimeOnStartup()

    // 初始化云开发
    this.initializeCloud()

    // 注意：积木管理器的初始化现在在云开发就绪后进行
    // this.initializeBrickManager() - 移动到 initializeBrickManagerAfterCloud

    // 暂时禁用登录检查，避免循环跳转
    // setTimeout(() => {
    //   this.checkNeedLogin()
    // }, 500)

    // 加载本地缓存数据（这个是同步的，可以立即执行）
    this.loadLocalData()

    // 监听网络状态变化
    this.setupNetworkListener()
  },

  /**
   * 应用启动时修复登录时间数据
   */
  fixLoginTimeOnStartup() {
    console.log('🔧 应用启动时检查并修复登录时间数据');

    try {
      const loginTime = wx.getStorageSync('login_time');
      const isLoggedIn = wx.getStorageSync('isLoggedIn');
      let userInfo = wx.getStorageSync('userInfo');
      let userId = wx.getStorageSync('userId');

      console.log('📋 启动时登录状态检查:', {
        loginTime: loginTime,
        loginTimeType: typeof loginTime,
        isLoggedIn: isLoggedIn,
        hasUserInfo: !!userInfo,
        hasUserId: !!userId
      });

      // 检查登录时间是否无效（包括空字符串） - 增强版
      const isLoginTimeInvalid = (
        loginTime === null ||
        loginTime === undefined ||
        loginTime === '' ||           // 专门处理空字符串
        loginTime === '0' ||          // 处理字符串'0'
        typeof loginTime !== 'number' ||
        isNaN(loginTime) ||
        loginTime <= 0 ||             // 处理负数或0
        loginTime > Date.now()        // 处理未来时间（异常情况）
      );

      // 额外的数据完整性检查
      const hasCompleteLoginData = (
        isLoggedIn &&
        userInfo &&
        userId &&
        !isLoginTimeInvalid
      );

      console.log('📊 应用启动时数据完整性检查:', {
        isLoggedIn: isLoggedIn,
        hasUserInfo: !!userInfo,
        hasUserId: !!userId,
        loginTimeValid: !isLoginTimeInvalid,
        hasCompleteLoginData: hasCompleteLoginData,
        loginTimeValue: loginTime,
        loginTimeType: typeof loginTime
      });

      // 智能修复逻辑 - 处理各种数据不完整的情况
      if (isLoggedIn && isLoginTimeInvalid) {
        console.log('🔧 检测到登录时间无效，开始智能修复');

        const currentTime = Date.now();
        const fixedLoginTime = currentTime - (2 * 60 * 60 * 1000); // 假设2小时前登录

        console.log('🔧 应用启动时修复无效登录时间:', {
          原始loginTime: loginTime,
          原始类型: typeof loginTime,
          修复后loginTime: fixedLoginTime,
          修复时间: new Date(fixedLoginTime).toLocaleString(),
          修复原因: isLoginTimeInvalid ? '登录时间无效' : '其他原因'
        });

        wx.setStorageSync('login_time', fixedLoginTime);

        // 如果用户信息缺失，也进行修复
        if (!userInfo) {
          const basicUserInfo = { nickName: '微信用户', avatarUrl: '' };
          wx.setStorageSync('userInfo', basicUserInfo);
          userInfo = basicUserInfo;
          console.log('🔧 已修复缺失的用户信息');
        }

        if (!userId) {
          const basicUserId = 'startup_user_' + Date.now();
          wx.setStorageSync('userId', basicUserId);
          userId = basicUserId;
          console.log('🔧 已修复缺失的用户ID');
        }

        // 更新全局状态
        this.globalData.isLoggedIn = true;
        this.globalData.userInfo = userInfo;

        console.log('✅ 应用启动时智能修复完成');

      } else if (hasCompleteLoginData) {
        console.log('✅ 登录数据完整，无需修复');

        // 更新全局状态
        this.globalData.isLoggedIn = true;
        this.globalData.userInfo = userInfo;

      } else {
        console.log('ℹ️ 用户未登录或数据不完整:', {
          isLoggedIn: isLoggedIn,
          hasUserInfo: !!userInfo,
          hasUserId: !!userId,
          loginTimeValid: !isLoginTimeInvalid
        });

        // 清理不一致的登录状态
        if (isLoggedIn && (!userInfo || !userId)) {
          console.log('🔧 清理不一致的登录状态');
          wx.setStorageSync('isLoggedIn', false);
          this.globalData.isLoggedIn = false;
        }
      }

    } catch (error) {
      console.error('❌ 应用启动时登录时间修复失败:', error);
    }
  },

  /**
   * 初始化基础服务
   */
  initializeServices() {
    try {
      // 加载API拦截器（最优先加载）
      const { apiInterceptor } = require('./utils/api-interceptor.js')
      this.apiInterceptor = apiInterceptor
      console.log('✅ API拦截器加载成功')

      // 加载系统信息管理器
      const { systemInfoManager } = require('./utils/system-info.js')
      this.systemInfoManager = systemInfoManager
      console.log('✅ 系统信息管理器加载成功')

      // 加载导航管理器
      this.navigation = require('./utils/navigation.js')
      console.log('✅ 导航管理器加载成功')

      // 加载状态管理器
      this.store = require('./utils/store.js')
      console.log('✅ 状态管理器加载成功')

      // 加载ApiService
      const apiModule = require('./utils/api.js')
      console.log('✅ ApiService模块加载成功')

      // 确保ApiService在app实例上可用
      if (apiModule) {
        this.ApiService = apiModule
        // global.ApiService已经在utils/api.js中设置
        console.log('✅ ApiService已注册到app实例')
      }

      // 加载HttpApiService
      const httpApiModule = require('./utils/http-api.js')
      console.log('✅ HttpApiService模块加载成功')

      // 确保HttpApiService在app实例和全局都可用
      if (httpApiModule) {
        this.HttpApiService = httpApiModule
        global.HttpApiService = httpApiModule
        console.log('✅ HttpApiService已注册到app实例和全局')
      }

      // 加载AsyncResumeAPI
      try {
        const asyncResumeModule = require('./utils/async-resume-api.js')
        if (asyncResumeModule) {
          this.AsyncResumeAPI = asyncResumeModule
          global.AsyncResumeAPI = asyncResumeModule
          console.log('✅ AsyncResumeAPI已注册到app实例和全局')
        }
      } catch (error) {
        console.warn('⚠️ AsyncResumeAPI加载失败:', error.message)
      }

      // SCF请求服务已移除，现在完全使用TCB云函数
      console.log('✅ 已切换到TCB云函数架构，无需SCF请求服务')

      // 初始化系统信息
      this.initializeSystemInfo()

    } catch (error) {
      console.error('❌ 基础服务初始化失败:', error)
    }
  },

  /**
   * 初始化系统信息
   */
  async initializeSystemInfo() {
    try {
      if (this.systemInfoManager) {
        await this.systemInfoManager.initialize();
        console.log('✅ 系统信息初始化完成');
      }
    } catch (error) {
      console.error('❌ 系统信息初始化失败:', error);
    }
  },

  /**
   * 初始化云开发
   */
  initializeCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      this.globalData.useMockData = true
      this.globalData.isCloudConnected = false
    } else {
      try {
        // 使用指定的云开发环境ID
        wx.cloud.init({
          env: this.globalData.cloudEnvId,
          traceUser: true,
        })
        console.log('✅ 云开发初始化成功，环境ID:', this.globalData.cloudEnvId)

        // 设置云开发连接状态
        this.globalData.isCloudConnected = true

        // 启用云开发连接测试
        setTimeout(() => {
          this.testCloudConnection()
        }, 1000)

        // 延迟初始化积木管理器，确保云开发完全就绪
        setTimeout(() => {
          this.initializeBrickManagerAfterCloud()
        }, 2000)
      } catch (error) {
        console.error('❌ 云开发初始化失败:', error)
        this.globalData.useMockData = true
        this.globalData.isCloudConnected = false
      }
    }
  },

  /**
   * 初始化积木管理器
   */
  async initializeBrickManager() {
    try {
      console.log('🧱 初始化积木管理器...')

      // 动态导入BrickManager
      const BrickManagerModule = require('./utils/brick-manager.js')
      const BrickManager = BrickManagerModule.default || BrickManagerModule.BrickManager || BrickManagerModule

      // 创建实例
      this.brickManager = new BrickManager()
      console.log('✅ 积木管理器实例创建成功')

    } catch (error) {
      console.error('❌ 积木管理器模块加载失败:', error)
    }
  },

  /**
   * 云开发就绪后初始化积木管理器
   */
  async initializeBrickManagerAfterCloud() {
    try {
      console.log('🔄 云开发就绪，开始初始化积木管理器...')

      // 初始化积木管理器
      await this.initializeBrickManager()

      // 如果积木管理器初始化成功，进行数据初始化
      if (this.brickManager) {
        try {
          await this.brickManager.init()
          console.log('✅ 积木管理器数据初始化完成')

          // 检查是否有待同步的积木
          setTimeout(() => {
            this.brickManager.syncPendingBricksToCloud().catch(err => {
              console.warn('⚠️ 启动时同步待同步积木失败:', err)
            })
          }, 3000)
        } catch (error) {
          console.error('❌ 积木管理器数据初始化失败:', error)
        }
      }
    } catch (error) {
      console.error('❌ 云开发就绪后积木管理器初始化失败:', error)
    }
  },

  onShow() {
    console.log('小程序显示')
    // 检查网络状态
    this.checkNetworkStatus()
    // 尝试同步积木数据
    this.syncBricksOnShow()
  },

  onHide() {
    console.log('小程序隐藏')
    // 保存数据到本地缓存
    this.saveLocalData()
  },

  /**
   * 小程序显示时同步积木数据
   */
  async syncBricksOnShow() {
    try {
      if (this.brickManager && this.globalData.isCloudConnected) {
        console.log('🔄 小程序显示时同步积木数据...')
        await this.brickManager.refreshFromCloud()
        console.log('✅ 积木数据同步完成')
      }
    } catch (error) {
      console.warn('⚠️ 积木数据同步失败:', error)
    }
  },

  /**
   * 设置网络状态监听
   */
  setupNetworkListener() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('📶 网络状态变化:', res)

      if (res.isConnected && !this.globalData.isCloudConnected) {
        console.log('🌐 网络已连接，尝试重新连接云开发...')
        // 重新测试云开发连接
        setTimeout(() => {
          this.testCloudConnection()
        }, 1000)

        // 尝试同步积木数据
        setTimeout(() => {
          this.syncBricksOnNetworkRestore()
        }, 2000)
      }
    })
  },

  /**
   * 网络恢复时同步积木数据
   */
  async syncBricksOnNetworkRestore() {
    try {
      if (this.brickManager) {
        console.log('🔄 网络恢复，同步积木数据...')
        await this.brickManager.syncToCloud()
        console.log('✅ 网络恢复同步完成')
      }
    } catch (error) {
      console.warn('⚠️ 网络恢复同步失败:', error)
    }
  },

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        if (this.store) {
          this.store.setNetworkStatus(res.networkType)
        }
        console.log('网络状态:', res.networkType)
      },
      fail: () => {
        if (this.store) {
          this.store.setNetworkStatus('unknown')
        }
      }
    })
  },

  // 检查用户登录状态 - 使用状态管理器
  checkUserLogin() {
    if (this.store) {
      const userState = this.store.getState('user')
      return userState.isLoggedIn && userState.userInfo
    }

    // 兼容处理：如果状态管理器未加载，使用原来的方法
    try {
      const isLoggedIn = wx.getStorageSync('isLoggedIn')
      const userInfo = wx.getStorageSync('userInfo')

      if (isLoggedIn && userInfo) {
        this.globalData.userInfo = userInfo
        this.globalData.isLoggedIn = true
        console.log('用户已登录:', userInfo.nickName)
        return true
      } else {
        console.log('用户未登录')
        this.globalData.isLoggedIn = false
        return false
      }
    } catch (e) {
      console.error('检查登录状态失败:', e)
      this.globalData.isLoggedIn = false
      return false
    }
  },

  // 加载本地缓存数据 - 兼容状态管理器
  loadLocalData() {
    if (this.store) {
      // 使用状态管理器，它会自动从本地存储初始化
      console.log('✅ 使用状态管理器加载本地数据')
      return
    }

    // 兼容处理：如果状态管理器未加载，使用原来的方法
    try {
      const bricks = wx.getStorageSync('bricks') || []
      const templates = wx.getStorageSync('templates') || []
      const theme = wx.getStorageSync('theme') || 'dark'

      this.globalData.bricks = bricks
      this.globalData.templates = templates
      this.globalData.theme = theme
    } catch (e) {
      console.error('加载本地数据失败:', e)
    }
  },

  // 保存数据到本地缓存 - 兼容状态管理器
  saveLocalData() {
    if (this.store) {
      // 状态管理器会自动持久化数据
      console.log('✅ 状态管理器自动持久化数据')
      return
    }

    // 兼容处理：如果状态管理器未加载，使用原来的方法
    try {
      wx.setStorageSync('bricks', this.globalData.bricks)
      wx.setStorageSync('templates', this.globalData.templates)
      wx.setStorageSync('theme', this.globalData.theme)
    } catch (e) {
      console.error('保存本地数据失败:', e)
    }
  },

  // 用户登录 - 使用状态管理器
  userLogin(userInfo, openid = null) {
    if (this.store) {
      this.store.login(userInfo, openid)
    } else {
      // 兼容处理
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      wx.setStorageSync('userInfo', userInfo)
      wx.setStorageSync('isLoggedIn', true)
      if (openid) {
        wx.setStorageSync('openid', openid)
      }
    }
    console.log('用户登录成功:', userInfo.nickName)

    // 触发积木管理器的登录后同步
    this.onUserLoginSuccess()
  },

  /**
   * 用户登录成功后的处理
   */
  async onUserLoginSuccess() {
    try {
      console.log('🔐 用户登录成功，开始处理后续任务...')

      // 如果积木管理器已初始化，触发登录后同步
      if (this.brickManager) {
        await this.brickManager.onUserLogin()
      }

      console.log('✅ 用户登录后处理完成')
    } catch (error) {
      console.error('❌ 用户登录后处理失败:', error)
    }
  },

  // 用户登出 - 使用状态管理器
  userLogout() {
    if (this.store) {
      this.store.logout()
    } else {
      // 兼容处理
      this.globalData.userInfo = null
      this.globalData.isLoggedIn = false
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('isLoggedIn')
      wx.removeStorageSync('openid')
    }
    console.log('用户已登出')
  },

  // 检查是否需要登录 - 修复版本，暂时禁用自动跳转
  checkNeedLogin() {
    const isLoggedIn = this.checkUserLogin()

    if (!isLoggedIn) {
      console.log('检查到用户未登录，但暂时不强制跳转')
      // 暂时注释掉自动跳转逻辑，避免循环跳转问题
      // 用户可以在需要时手动进入登录页面
      return false
    }

    return false
  },

  // 全局错误处理
  onError(msg) {
    console.error('小程序错误:', msg)
    // 对于 invoke too early 错误，不弹出提示，静默处理
    if (msg && typeof msg === 'string' && msg.includes('invoke too eayly')) {
      console.log('检测到invoke too early错误，已静默处理')
      return
    }
  },

  // 全局未处理的Promise拒绝 - 增强版
  onUnhandledRejection(res) {
    console.error('未处理的Promise拒绝:', res);

    // 详细记录Promise拒绝信息
    const errorInfo = {
      reason: res.reason,
      promise: res.promise,
      timestamp: new Date().toISOString(),
      stack: res.reason?.stack
    };

    console.error('Promise拒绝详情:', errorInfo);

    // 检查是否是导航相关的错误
    if (res.reason && (
      res.reason.message?.includes('switchTab') ||
      res.reason.message?.includes('navigateTo') ||
      res.reason.message?.includes('redirectTo')
    )) {
      console.error('🔄 检测到导航相关的Promise拒绝');

      // 尝试恢复导航状态
      try {
        const NavigationTimeoutHandler = require('./utils/navigation-timeout-handler.js');
        NavigationTimeoutHandler.optimizeNavigationPerformance();
      } catch (error) {
        console.error('导航状态恢复失败:', error);
      }
    }

    // 记录到全局错误统计
    if (!this.globalData.errorStats) {
      this.globalData.errorStats = {
        promiseRejections: 0,
        navigationErrors: 0,
        lastError: null
      };
    }

    this.globalData.errorStats.promiseRejections++;
    this.globalData.errorStats.lastError = errorInfo;

    if (res.reason?.message?.includes('switchTab') ||
      res.reason?.message?.includes('navigat')) {
      this.globalData.errorStats.navigationErrors++;
    }
  },

  // 全局安全导航方法
  async safeSwitchTab(options) {
    try {
      const NavigationTimeoutHandler = require('./utils/navigation-timeout-handler.js');
      return await NavigationTimeoutHandler.safeSwitchTab(options);
    } catch (error) {
      console.error('全局安全导航失败:', error);
      throw error;
    }
  },

  // 获取导航性能报告
  getNavigationReport() {
    try {
      const NavigationTimeoutHandler = require('./utils/navigation-timeout-handler.js');
      return NavigationTimeoutHandler.getNavigationReport();
    } catch (error) {
      console.error('获取导航报告失败:', error);
      return null;
    }
  },

  // 测试云开发连接
  async testCloudConnection() {
    try {
      console.log('🔄 测试云开发连接...')

      // 优先测试云函数连接
      try {
        const result = await wx.cloud.callFunction({
          name: 'ping',
          data: {
            test: true,
            timestamp: Date.now(),
            source: 'app-connection-test'
          }
        })

        if (result && result.result && result.result.success) {
          console.log('✅ 云函数连接测试成功:', result.result)
          this.globalData.isCloudConnected = true

          if (this.store) {
            this.store.setNetworkStatus('cloud-connected')
          }
          return
        }
      } catch (functionError) {
        console.log('⚠️ 云函数连接失败，尝试数据库连接:', functionError.message)
      }

      // 降级测试：数据库连接
      const db = wx.cloud.database()
      const testResult = await db.collection('users').limit(1).get()

      console.log('✅ 云数据库连接测试成功:', testResult)
      this.globalData.isCloudConnected = true

      if (this.store) {
        this.store.setNetworkStatus('cloud-connected')
      }

    } catch (error) {
      console.log('⚠️ 云开发连接测试失败，切换到模拟数据模式:', error.message || error)
      this.globalData.isCloudConnected = false
      this.globalData.useMockData = true

      if (this.store) {
        this.store.setNetworkStatus('cloud-disconnected')
      }
    }
  },

  // 设置模拟数据模式
  setMockDataMode(useMock) {
    this.globalData.useMockData = useMock
    console.log(useMock ? '切换到模拟数据模式' : '切换到云开发模式')
  },

  // 获取系统信息（新API）
  getSystemInfo() {
    if (this.systemInfoManager) {
      return this.systemInfoManager.getSystemInfo()
    }
    return null
  },

  // 检查权限状态
  checkPermission(permission) {
    if (this.systemInfoManager) {
      return this.systemInfoManager.checkPermission(permission)
    }
    return 'unknown'
  },

  // ===== 新增：便捷访问方法 =====

  /**
   * 获取导航管理器
   */
  getNavigation() {
    return this.navigation
  },

  /**
   * 获取状态管理器
   */
  getStore() {
    return this.store
  },

  /**
   * 导航到指定页面（便捷方法）
   */
  navigateTo(routeName, options = {}) {
    if (this.navigation) {
      return this.navigation.navigateTo(routeName, options)
    } else {
      console.error('导航管理器未初始化')
      return Promise.reject(new Error('导航管理器未初始化'))
    }
  },

  /**
   * 设置全局状态（便捷方法）
   */
  setState(path, value) {
    if (this.store) {
      return this.store.setState(path, value)
    } else {
      console.error('状态管理器未初始化')
    }
  },

  /**
   * 获取全局状态（便捷方法）
   */
  getState(path) {
    if (this.store) {
      return this.store.getState(path)
    } else {
      console.error('状态管理器未初始化')
      return undefined
    }
  }
}) 