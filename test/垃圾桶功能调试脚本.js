/**
 * 垃圾桶功能调试脚本
 * 用于排查垃圾桶按钮无反应的问题
 */

console.log('🔍 开始垃圾桶功能调试...\n');

// 1. 检查WXML事件绑定
console.log('1️⃣ WXML事件绑定检查:');
console.log('✅ 事件绑定: bindtap="clearAllBricks"');
console.log('✅ 显示条件: {{!isSelectMode && totalCount > 0}} (已修复)');
console.log('   原条件: {{!isSelectMode && abilityCount > 0}} (可能导致按钮不显示)\n');

// 2. 检查数据字段初始值
console.log('2️⃣ 数据字段初始值检查:');
const initialData = {
  isSelectMode: false,
  abilityCount: 0,
  totalCount: 0,
  bricks: []
};
console.log('📊 初始数据:', initialData);
console.log('⚠️  问题发现: totalCount初始为0，需要数据加载后才会更新\n');

// 3. 检查方法存在性
console.log('3️⃣ JS方法存在性检查:');
console.log('✅ clearAllBricks() 方法存在');
console.log('✅ performClearAllBricks() 方法存在');
console.log('✅ updateCounts() 方法存在\n');

// 4. 模拟按钮显示条件检查
console.log('4️⃣ 按钮显示条件模拟:');

function checkButtonVisibility(isSelectMode, totalCount) {
  const shouldShow = !isSelectMode && totalCount > 0;
  console.log(`   isSelectMode: ${isSelectMode}, totalCount: ${totalCount} => 显示: ${shouldShow}`);
  return shouldShow;
}

console.log('场景测试:');
checkButtonVisibility(false, 0);  // 初始状态
checkButtonVisibility(false, 5);  // 有数据
checkButtonVisibility(true, 5);   // 选择模式
console.log('');

// 5. 数据加载流程检查
console.log('5️⃣ 数据加载流程检查:');
console.log('页面生命周期:');
console.log('  onLoad() -> checkLoginStatus()');
console.log('  onShow() -> cleanupMockData() -> loadBricksList() -> loadStats()');
console.log('');
console.log('数据更新流程:');
console.log('  loadBricksList() -> updateCounts() -> setData({totalCount})');
console.log('  updateCounts() -> 计算各类积木数量 -> 更新totalCount');
console.log('');

// 6. 可能的问题原因
console.log('6️⃣ 可能的问题原因分析:');
console.log('❌ 原因1: abilityCount条件过于严格');
console.log('   - 只有技能和项目类积木才计入abilityCount');
console.log('   - 如果只有个人信息、教育背景等积木，abilityCount=0');
console.log('   - 解决方案: 改为totalCount > 0');
console.log('');
console.log('❌ 原因2: 数据加载失败');
console.log('   - 云数据库连接问题');
console.log('   - 用户登录状态问题');
console.log('   - 本地存储数据为空');
console.log('');
console.log('❌ 原因3: updateCounts()未被调用');
console.log('   - loadBricksList()执行失败');
console.log('   - 异步操作时序问题');
console.log('');

// 7. 修复方案
console.log('7️⃣ 修复方案:');
console.log('✅ 修复1: 更改显示条件');
console.log('   从 abilityCount > 0 改为 totalCount > 0');
console.log('');
console.log('✅ 修复2: 添加调试日志');
console.log('   在clearAllBricks()开头添加console.log');
console.log('   在updateCounts()中添加按钮显示条件检查');
console.log('');
console.log('✅ 修复3: 强制显示按钮（临时调试）');
console.log('   可以临时移除wx:if条件，确认事件绑定是否正常');
console.log('');

// 8. 测试步骤
console.log('8️⃣ 建议测试步骤:');
console.log('1. 打开微信开发者工具控制台');
console.log('2. 进入积木库页面');
console.log('3. 查看控制台日志，确认:');
console.log('   - 数据加载是否成功');
console.log('   - updateCounts()是否被调用');
console.log('   - totalCount是否大于0');
console.log('   - 垃圾桶按钮显示条件是否满足');
console.log('4. 如果按钮仍不显示，临时移除wx:if条件测试');
console.log('5. 点击按钮，查看clearAllBricks()是否被调用');
console.log('');

// 9. 云函数测试
console.log('9️⃣ 云函数测试建议:');
console.log('可以在控制台直接测试云函数:');
console.log(`
wx.cloud.callFunction({
  name: 'brickManager',
  data: {
    action: 'clearAll',
    data: { confirm: true }
  }
}).then(res => {
  console.log('云函数测试结果:', res);
}).catch(err => {
  console.error('云函数测试失败:', err);
});
`);

console.log('🎯 调试完成！请按照上述步骤进行排查。');
